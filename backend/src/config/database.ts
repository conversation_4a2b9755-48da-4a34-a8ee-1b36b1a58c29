import mongoose from 'mongoose';

const connectDB = async (): Promise<void> => {
  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/flex-living-reviews';
    
    await mongoose.connect(mongoURI);
    
    console.log('MongoDB connected successfully');
  } catch (error) {
    console.warn('MongoDB connection failed, continuing without database:', error);
    // Don't exit process, continue without database for demo purposes
  }
};

export default connectDB;

