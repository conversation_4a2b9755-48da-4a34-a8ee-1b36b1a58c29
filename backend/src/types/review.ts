export interface ReviewCategory {
  category: string;
  rating: number;
}

export interface HostawayReview {
  id: number;
  type: string;
  status: string;
  rating: number | null;
  publicReview: string;
  reviewCategory: ReviewCategory[];
  submittedAt: string;
  guestName: string;
  listingName: string;
}

export interface NormalizedReview {
  id: string;
  propertyName: string;
  guestName: string;
  rating: number | null;
  overallRating?: number;
  review: string;
  categories: ReviewCategory[];
  submittedAt: Date;
  type: string;
  status: string;
  channel: string;
  isApproved: boolean;
  isDisplayed: boolean;
}

export interface HostawayApiResponse {
  status: string;
  result: HostawayReview[];
}

