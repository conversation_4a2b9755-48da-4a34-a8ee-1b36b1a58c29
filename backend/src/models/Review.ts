import mongoose, { Schema, Document } from 'mongoose';
import { ReviewCategory } from '../types/review';

export interface IReview extends Document {
  id: string;
  propertyName: string;
  guestName: string;
  rating: number | null;
  overallRating?: number;
  review: string;
  categories: ReviewCategory[];
  submittedAt: Date;
  type: string;
  status: string;
  channel: string;
  isApproved: boolean;
  isDisplayed: boolean;
}

const ReviewCategorySchema = new Schema<ReviewCategory>({
  category: { type: String, required: true },
  rating: { type: Number, required: true, min: 1, max: 10 }
});

const ReviewSchema = new Schema<IReview>({
  id: { type: String, required: true, unique: true },
  propertyName: { type: String, required: true },
  guestName: { type: String, required: true },
  rating: { type: Number, default: null },
  overallRating: { type: Number, default: null },
  review: { type: String, required: true },
  categories: [ReviewCategorySchema],
  submittedAt: { type: Date, required: true },
  type: { type: String, required: true },
  status: { type: String, required: true },
  channel: { type: String, required: true, default: 'hostaway' },
  isApproved: { type: Boolean, default: false },
  isDisplayed: { type: Boolean, default: false }
}, {
  timestamps: true
});

export default mongoose.model<IReview>('Review', ReviewSchema);

