import express from 'express';
import {
  getHostawayReviews,
  getAllReviews,
  updateReviewStatus,
  getDisplayedReviews,
  getReviewStats
} from '../controllers/reviewController';

const router = express.Router();

// Hostaway integration endpoint
router.get('/hostaway', getHostawayReviews);

// Get all reviews with filtering and pagination
router.get('/', getAllReviews);

// Get reviews for public display
router.get('/displayed', getDisplayedReviews);

// Get review statistics
router.get('/stats', getReviewStats);

// Update review approval/display status
router.patch('/:id/status', updateReviewStatus);

export default router;

