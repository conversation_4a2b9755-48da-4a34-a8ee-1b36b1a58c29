import { Request, Response } from 'express';
import { HostawayReview, NormalizedReview, HostawayApiResponse } from '../types/review';

// In-memory storage for demo purposes
let reviewsStorage: NormalizedReview[] = [];

// Mock Hostaway API data based on the PDF example
const mockHostawayData: HostawayApiResponse = {
  status: "success",
  result: [
    {
      id: 7453,
      type: "host-to-guest",
      status: "published",
      rating: null,
      publicReview: "<PERSON> and family are wonderful! Would definitely host again :)",
      reviewCategory: [
        { category: "cleanliness", rating: 10 },
        { category: "communication", rating: 10 },
        { category: "respect_house_rules", rating: 10 }
      ],
      submittedAt: "2020-08-21 22:45:14",
      guestName: "<PERSON>",
      listingName: "2B N1 A - 29 Shoreditch Heights"
    },
    {
      id: 7454,
      type: "guest-to-host",
      status: "published",
      rating: 9,
      publicReview: "Amazing property in the heart of Shoreditch. Clean, modern, and perfectly located. The host was very responsive and helpful throughout our stay.",
      reviewCategory: [
        { category: "cleanliness", rating: 9 },
        { category: "communication", rating: 10 },
        { category: "location", rating: 10 },
        { category: "value", rating: 8 }
      ],
      submittedAt: "2024-01-15 14:30:22",
      guestName: "Emma Thompson",
      listingName: "2B N1 A - 29 Shoreditch Heights"
    },
    {
      id: 7455,
      type: "guest-to-host",
      status: "published",
      rating: 8,
      publicReview: "Great apartment with excellent amenities. The kitchen was well-equipped and the workspace was perfect for remote work. Minor issue with WiFi on the first day but quickly resolved.",
      reviewCategory: [
        { category: "cleanliness", rating: 9 },
        { category: "communication", rating: 8 },
        { category: "amenities", rating: 9 },
        { category: "value", rating: 7 }
      ],
      submittedAt: "2024-02-03 09:15:45",
      guestName: "Marcus Johnson",
      listingName: "1B S2 C - 15 Camden Lock"
    },
    {
      id: 7456,
      type: "guest-to-host",
      status: "published",
      rating: 10,
      publicReview: "Exceptional stay! The property exceeded all expectations. Beautifully designed, spotlessly clean, and in a fantastic location. Will definitely book again.",
      reviewCategory: [
        { category: "cleanliness", rating: 10 },
        { category: "communication", rating: 10 },
        { category: "location", rating: 10 },
        { category: "value", rating: 10 }
      ],
      submittedAt: "2024-02-20 16:45:12",
      guestName: "Sophie Chen",
      listingName: "Studio W1 - 42 Fitzrovia Square"
    },
    {
      id: 7457,
      type: "guest-to-host",
      status: "published",
      rating: 7,
      publicReview: "Good property overall but had some maintenance issues. The heating wasn't working properly during our winter stay. Location is excellent though.",
      reviewCategory: [
        { category: "cleanliness", rating: 8 },
        { category: "communication", rating: 9 },
        { category: "location", rating: 9 },
        { category: "maintenance", rating: 5 }
      ],
      submittedAt: "2024-03-10 11:20:33",
      guestName: "David Wilson",
      listingName: "1B S2 C - 15 Camden Lock"
    }
  ]
};

const normalizeHostawayReview = (hostawayReview: HostawayReview): NormalizedReview => {
  // Calculate overall rating from categories if main rating is null
  let overallRating = hostawayReview.rating;
  if (!overallRating && hostawayReview.reviewCategory.length > 0) {
    const sum = hostawayReview.reviewCategory.reduce((acc, cat) => acc + cat.rating, 0);
    overallRating = Math.round(sum / hostawayReview.reviewCategory.length);
  }

  return {
    id: hostawayReview.id.toString(),
    propertyName: hostawayReview.listingName,
    guestName: hostawayReview.guestName,
    rating: hostawayReview.rating,
    overallRating: overallRating || undefined,
    review: hostawayReview.publicReview,
    categories: hostawayReview.reviewCategory,
    submittedAt: new Date(hostawayReview.submittedAt),
    type: hostawayReview.type,
    status: hostawayReview.status,
    channel: 'hostaway',
    isApproved: false,
    isDisplayed: false
  };
};

export const getHostawayReviews = async (req: Request, res: Response) => {
  try {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const normalizedReviews = mockHostawayData.result.map(normalizeHostawayReview);
    
    // Store in memory
    normalizedReviews.forEach(review => {
      const existingIndex = reviewsStorage.findIndex(r => r.id === review.id);
      if (existingIndex >= 0) {
        reviewsStorage[existingIndex] = review;
      } else {
        reviewsStorage.push(review);
      }
    });
    
    res.json({
      success: true,
      data: normalizedReviews,
      count: normalizedReviews.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to fetch Hostaway reviews',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

export const getAllReviews = async (req: Request, res: Response) => {
  try {
    const { 
      property, 
      rating, 
      category, 
      channel, 
      sortBy = 'submittedAt', 
      sortOrder = 'desc',
      page = 1,
      limit = 10
    } = req.query;

    let filteredReviews = [...reviewsStorage];
    
    if (property) {
      filteredReviews = filteredReviews.filter(review => 
        review.propertyName.toLowerCase().includes((property as string).toLowerCase())
      );
    }
    
    if (rating) {
      filteredReviews = filteredReviews.filter(review => 
        review.overallRating && review.overallRating >= parseInt(rating as string)
      );
    }
    
    if (channel) {
      filteredReviews = filteredReviews.filter(review => review.channel === channel);
    }
    
    if (category) {
      filteredReviews = filteredReviews.filter(review =>
        review.categories.some(cat => cat.category === category)
      );
    }

    // Sort reviews
    filteredReviews.sort((a, b) => {
      let aValue: any = a[sortBy as keyof NormalizedReview];
      let bValue: any = b[sortBy as keyof NormalizedReview];
      
      if (sortBy === 'submittedAt') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }
      
      if (sortOrder === 'desc') {
        return bValue > aValue ? 1 : -1;
      } else {
        return aValue > bValue ? 1 : -1;
      }
    });

    const skip = (parseInt(page as string) - 1) * parseInt(limit as string);
    const paginatedReviews = filteredReviews.slice(skip, skip + parseInt(limit as string));

    res.json({
      success: true,
      data: paginatedReviews,
      pagination: {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        total: filteredReviews.length,
        pages: Math.ceil(filteredReviews.length / parseInt(limit as string))
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to fetch reviews',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

export const updateReviewStatus = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { isApproved, isDisplayed } = req.body;

    const reviewIndex = reviewsStorage.findIndex(review => review.id === id);
    
    if (reviewIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Review not found'
      });
    }

    if (isApproved !== undefined) {
      reviewsStorage[reviewIndex].isApproved = isApproved;
    }
    
    if (isDisplayed !== undefined) {
      reviewsStorage[reviewIndex].isDisplayed = isDisplayed;
    }

    res.json({
      success: true,
      data: reviewsStorage[reviewIndex]
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to update review status',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

export const getDisplayedReviews = async (req: Request, res: Response) => {
  try {
    const { property } = req.query;
    
    let filteredReviews = reviewsStorage.filter(review => review.isDisplayed);
    
    if (property) {
      filteredReviews = filteredReviews.filter(review => 
        review.propertyName.toLowerCase().includes((property as string).toLowerCase())
      );
    }

    filteredReviews.sort((a, b) => new Date(b.submittedAt).getTime() - new Date(a.submittedAt).getTime());

    res.json({
      success: true,
      data: filteredReviews,
      count: filteredReviews.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to fetch displayed reviews',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

export const getReviewStats = async (req: Request, res: Response) => {
  try {
    const propertyStats = new Map();
    
    reviewsStorage.forEach(review => {
      const propertyName = review.propertyName;
      
      if (!propertyStats.has(propertyName)) {
        propertyStats.set(propertyName, {
          _id: propertyName,
          totalReviews: 0,
          totalRating: 0,
          ratingCount: 0,
          approvedReviews: 0,
          displayedReviews: 0
        });
      }
      
      const stats = propertyStats.get(propertyName);
      stats.totalReviews++;
      
      if (review.overallRating) {
        stats.totalRating += review.overallRating;
        stats.ratingCount++;
      }
      
      if (review.isApproved) {
        stats.approvedReviews++;
      }
      
      if (review.isDisplayed) {
        stats.displayedReviews++;
      }
    });

    const statsArray = Array.from(propertyStats.values()).map(stat => ({
      ...stat,
      averageRating: stat.ratingCount > 0 ? stat.totalRating / stat.ratingCount : 0
    })).sort((a, b) => b.totalReviews - a.totalReviews);

    res.json({
      success: true,
      data: statsArray
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to fetch review statistics',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

